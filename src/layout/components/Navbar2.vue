<template>
  <div class="navbar">
    <logo :collapse="false" />
    <hamburger
      v-if="showHamburger"
      id="hamburger-container"
      :is-active="sidebar.opened"
      class="hamburger-container"
      @toggleClick="toggleSideBar"
    />

    <!-- <breadcrumb id="breadcrumb-container" class="breadcrumb-container" v-if="!topNav"/> -->
    <top-nav id="topmenu-container" class="topmenu-container" v-if="topNav" />

    <div class="right-menu">
      <!-- <template v-if="device!=='mobile'">
        <search id="header-search" class="right-menu-item" />

        <screenfull id="screenfull" class="right-menu-item hover-effect" /> -->

      <!-- <el-tooltip content="布局大小" effect="dark" placement="bottom">
          <size-select id="size-select" class="right-menu-item hover-effect" />
        </el-tooltip> -->
      <!-- 中英文切换 -->
      <!-- </template> -->
      <template>
        <div
          class="language-change right-menu-item hover-effect"
          @click="onToggleLanguageClick(isZh() ? 'en' : 'zh')"
        >
          <svg-icon :icon-class="isZh() ? 'en' : 'zh'" />
        </div>
      </template>

      <el-dropdown
        class="avatar-container right-menu-item hover-effect"
        trigger="hover"
        v-if="name"
      >
        <div class="avatar-wrapper">
          <span>{{ name }}</span>
          <i class="el-icon-caret-bottom" />
        </div>
        <el-dropdown-menu slot="dropdown">
          <!-- <router-link to="/user/profile">
          </router-link> -->
          <el-dropdown-item
            @click.native="onProfileClick"
            v-if="userInfo.roleNames !== 'admin'"
            >{{ $t("profile.changePassword") }}</el-dropdown-item
          >
          <el-dropdown-item divided @click.native="logout">
            <span>{{ $t("common.logout") }}</span>
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>
    <profile
      :dialogVisible="dialogVisible"
      @eventClose="eventClose"
      @eventSucc="eventSucc"
    />
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import Breadcrumb from "@/components/Breadcrumb";
import TopNav from "@/components/TopNav";
import Hamburger from "@/components/Hamburger";
import Screenfull from "@/components/Screenfull";
import SizeSelect from "@/components/SizeSelect";
import Search from "@/components/HeaderSearch";
import Profile from "./profile/index";
import Logo from "./Sidebar/Logo";

export default {
  components: {
    Breadcrumb,
    TopNav,
    Hamburger,
    Screenfull,
    SizeSelect,
    Search,
    Profile,
    Logo,
  },
  computed: {
    ...mapGetters(["sidebar", "name", "device", "userInfo"]),
    topNav: {
      get() {
        return this.$store.state.settings.topNav;
      },
    },
    visitedViews() {
      return this.$store.state.tagsView.visitedViews;
    },
    setting: {
      get() {
        return this.$store.state.settings.showSettings;
      },
      set(val) {
        this.$store.dispatch("settings/changeSetting", {
          key: "showSettings",
          value: val,
        });
      },
    },
    // 用户名-口令登录且首次登录时需强制修改密码
    forceDig() {
      return (
        (this.$store.state.user.isFirstLogin == 1 ||
          this.$store.state.user.isLoginDateDue == 1) &&
        this.$store.state.user.userInfo.userType == 1
      );
    },
  },
  props: {
    showHamburger: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      language: this.getLanguage(),
      dialogVisible: false,
    };
  },
  methods: {
    toggleSideBar() {
      this.$store.dispatch("app/toggleSideBar");
    },
    onToggleLanguageClick(item) {
      this.$i18n.locale = item;
      this.setLanguage(item);
      // 需要往子项目中传入切换的值
      const existIframe = document.getElementsByTagName("iframe");
      for (let i = 0; i < existIframe.length; i += 1) {
        existIframe[i].contentWindow.postMessage(
          {
            type: "language",
            languageVal: item,
          },
          existIframe[i].src,
        );
      }
      location.reload();
    },
    async logout() {
      this.confirmMessage(this.$t("common.logoutTip"))
        .then(() => {
          let url = this.getLogoutURL();
          this.$store.dispatch("LogOut").then(() => {
            if (url) {
              // 先白屏再跳转
              this.$store.dispatch("app/setGlobalLoading", true);
              location.href = url;
            } else {
              location.href = `${this.getBasePrefix()}login`;
            }
          });
        })
        .catch(() => {});
    },
    onProfileClick() {
      this.dialogVisible = true;
    },
    eventClose() {
      this.dialogVisible = false;
    },
    eventSucc() {
      this.confirmMessage(
        this.$t("profile.reLoginTip"),
        "",
        false,
        false,
        false,
      ).then((res) => {
        let url = this.getLogoutURL();
        this.$store.dispatch("LogOut").then(() => {
          if (url) {
            this.$store.dispatch("app/setGlobalLoading", true);
            location.href = url;
          } else {
            location.href = `${this.getBasePrefix()}login`;
          }
        });
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.navbar {
  height: 56px;
  overflow: hidden;
  position: relative;
  background: var(--custom-top-bg);
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);

  .hamburger-container {
    line-height: 56px;
    height: 100%;
    float: left;
    cursor: pointer;
    transition: background 0.3s;
    -webkit-tap-highlight-color: transparent;

    &:hover {
      background: rgba(0, 0, 0, 0.025);
    }
  }

  .breadcrumb-container {
    float: left;
  }

  .topmenu-container {
    position: absolute;
    left: 50px;
  }

  .errLog-container {
    display: inline-block;
    vertical-align: top;
  }

  .right-menu {
    float: right;
    height: 100%;
    line-height: 56px;

    &:focus {
      outline: none;
    }

    .right-menu-item {
      display: inline-block;
      padding: 0 8px;
      height: 100%;
      font-size: 14px;
      color: var(--custom-top-svg) !important;
      vertical-align: text-bottom;
      line-height: 56px;
      &.hover-effect {
        cursor: pointer;
        transition: background 0.3s;

        &:hover {
          background: rgba(0, 0, 0, 0.025);
        }
      }
    }
    .language-change {
      line-height: 56px;
      .svg-icon {
        width: 24px;
        height: 24px;
        vertical-align: middle;
      }
    }

    .avatar-container {
      margin-right: 30px;

      .avatar-wrapper {
        // margin-top: 5px;
        position: relative;

        .user-avatar {
          cursor: pointer;
          width: 40px;
          height: 40px;
          border-radius: 10px;
        }

        .el-icon-caret-bottom {
          cursor: pointer;
          position: absolute;
          right: -16px;
          top: 23px;
          font-size: 12px;
        }
      }
    }
  }
}
</style>

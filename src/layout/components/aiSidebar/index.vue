<template>
  <div
    class="ai-sidebar-container"
    :class="{ 'has-logo': showLogo }"
    :style="{
      backgroundColor:
        settings.sideTheme == 'theme-custom'
          ? settings.menuBackgroundCustom
          : settings.sideTheme === 'theme-dark'
          ? variables.menuBackground
          : settings.sideTheme === 'theme-theme'
          ? settings.theme
          : variables.menuLightBackground,
    }"
  >
    <el-scrollbar
      :class="settings.sideTheme"
      wrap-class="scrollbar-wrapper"
      view-class="scrollbar-view"
    >
      <el-menu
        :default-active="activeMenu"
        :collapse="isCollapse"
        :background-color="
          settings.sideTheme == 'theme-custom'
            ? settings.menuBackgroundCustom
            : settings.sideTheme === 'theme-dark'
            ? variables.menuBackground
            : settings.sideTheme === 'theme-theme'
            ? settings.theme
            : variables.menuLightBackground
        "
        :text-color="
          settings.sideTheme == 'theme-custom'
            ? settings.menuColorCustom
            : settings.sideTheme === 'theme-dark' ||
              settings.sideTheme === 'theme-theme'
            ? variables.menuColor
            : variables.menuLightColor
        "
        :unique-opened="true"
        :active-text-color="settings.theme"
        :collapse-transition="false"
        mode="vertical"
      >
        <sidebar-item
          v-for="(route, index) in sidebarRouters"
          :key="route.path + index"
          :item="route"
          :base-path="route.path"
        />
      </el-menu>
      <div
        class="title"
        :class="{ 'collapsed-hidden': isCollapse }"
        style="
          font-size: 14px;
          padding-left: 20px;
          padding-right: 6px;
          height: 50px;
          color: #666666;
          line-height: 50px;
          font-weight: bold;
        "
      >
        {{ $t("session.historySession") }}
      </div>

      <div class="chat-list">
        <div v-for="group in groupedSessions" :key="group.title">
          <div class="group-header" v-if="group.sessions.length && !isCollapse">
            {{ group.title }}
          </div>
          <div
            class="chat-item"
            :class="{
              'is-active': i.conversationId == conversationId,
              'collapsed': isCollapse
            }"
            v-for="i in group.sessions"
            :key="i.conversationId"
            @click="openDify(i)"
          >
            <div class="app-icon">
              <div
                class="emoji-icon"
                :style="{ background: i.iconBackground }"
                v-if="i.iconType == 'emoji'"
              >
                <emoji
                  :data="emojiData"
                  :emoji="i.icon"
                  :native="true"
                  :size="20"
                />
                <!-- {{ i.icon }} -->
              </div>
              <img v-else :src="i.sourceUrl" object-fit="contain" />
            </div>
            <div class="chat-item-name" :class="{ 'collapsed-hidden': isCollapse }" :title="i.conversationName">
              {{ i.conversationName }}
            </div>
            <more-btns :row="i" :btns="btns" :limit="0" :class="{ 'collapsed-hidden': isCollapse }" />
            <!-- <el-popconfirm
          placement="top"
          title="确定删除会话吗？"
          @confirm="deleteSession(i)"
        >
          <i
            class="chait-item-remove el-icon el-icon-close"
            slot="reference"
          ></i>
        </el-popconfirm> -->
          </div>
        </div>
      </div>
    </el-scrollbar>
    <sidebar-footer :collapse="isCollapse" />

    <!-- 抽屉切换按钮 -->
    <div class="drawer-toggle-btn" @click="toggleSideBar">
      <svg
        :class="{ 'is-collapsed': isCollapse }"
        class="drawer-icon"
        viewBox="0 0 1024 1024"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M408 442h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8zm-8 204c0 4.4 3.6 8 8 8h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56zm504-486H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 632H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM142.4 642.1L298.7 519a8.84 8.84 0 0 0 0-13.9L142.4 381.9c-5.8-4.6-14.4-.5-14.4 6.9v246.3a8.9 8.9 0 0 0 14.4 7z"
        />
      </svg>
    </div>
  </div>
</template>

<script>
import { mapGetters, mapState } from "vuex";
import SidebarItem from "../Sidebar/SidebarItem";
import variables from "@/assets/styles/variables.scss";
import sidebarFooter from "../Sidebar/siderFooter.vue";
import {
  getSessionList,
  deleteSession,
  editSession,
} from "@/api/ai/difySession";
import data from "emoji-mart-vue-fast/data/all.json";
import { Emoji, EmojiIndex } from "emoji-mart-vue-fast";
const emojiData = new EmojiIndex(data);
export default {
  name: "AiSidebar",
  components: { SidebarItem, sidebarFooter, Emoji },
  data() {
    return {
      emojiData,
      // 自定义颜色
      menuColorCustom: this.$store.state.settings.menuColorCustom,
      menuColorActiveCustom: this.$store.state.settings.menuColorActiveCustom,
      menuBackgroundCustom: this.$store.state.settings.menuBackgroundCustom,
      sessionList: [],
      groupedSessions: [],
      btns: [
        {
          name: this.$t("common.rename"),
          id: "detailBtnId",
          class: "oper-text-btn",
          permi: ["none:none:none"],
          func: this.editName,
        },
        {
          name: this.$t("common.delete"),
          id: "detailBtnId",
          class: "delete-text-btn",
          permi: ["none:none:none"],
          func: this.deleteSession,
        },
      ],
    };
  },
  computed: {
    ...mapState(["settings"]),
    ...mapGetters(["sidebarRouters", "sidebar"]),
    activeMenu() {
      const route = this.$route;
      const { meta, path } = route;
      // if set path, the sidebar will highlight the path you set
      if (meta.activeMenu) {
        return meta.activeMenu;
      }
      return path;
    },
    conversationId() {
      const route = this.$route;
      return route?.query?.conversationId || "";
    },
    showLogo() {
      return false; //this.$store.state.settings.sidebarLogo;
    },
    variables() {
      return variables;
    },
    isCollapse() {
      return !this.sidebar.opened;
    },
  },
  watch: {
    // 监听设备类型变化，移动端时自动折叠侧边栏
    device(newDevice) {
      debugger
      if (newDevice === 'mobile' && this.sidebar.opened) {
        this.$store.dispatch("app/closeSideBar", { withoutAnimation: true });
      }
    }
  },
  created() {
    this.getSessionList();
    window.$eventBus.$on("refreshSessionList", this.getSessionList);
  },
  methods: {
    toggleSideBar() {
      // this.$store.dispatch("app/closeSideBar", { withoutAnimation: false });
      this.$store.dispatch("app/toggleSideBar");
    },
    // watchLocalStorage() {
    //   let conversationIdInfo =
    //     JSON.parse(localStorage.getItem("conversationIdInfo")) || {};
    //   let sessionIds = [];
    //   Object.values(conversationIdInfo).forEach((item) => {
    //     sessionIds = sessionIds.concat(Object.values(item));
    //   });
    //   let list = this.sessionList.map((i) => {
    //     return i.conversationId;
    //   });
    //   //如果sessionids有list之外的数据
    //   if (sessionIds.some((i) => !list.includes(i))) {
    //     this.getSessionList();
    //   } else {
    //     setTimeout(() => {
    //       this.watchLocalStorage();
    //     }, 1000);
    //   }
    // },
    getSessionList() {
      getSessionList({}).then((res) => {
        this.sessionList = res.data;
        // this.watchLocalStorage();
        this.groupByTime();
      });
    },
    //按会话更新时间分组
    groupByTime() {
      const now = new Date();
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);
      const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);

      const groups = [
        { title: this.$t("common.today"), sessions: [] },
        { title: this.$t("common.yesterday"), sessions: [] },
        { title: this.$t("common.beforeWeek"), sessions: [] },
        { title: this.$t("common.moreTime"), sessions: [] },
      ];

      this.sessionList.forEach((session) => {
        const sessionDate = new Date(session.updatedAt);
        const sessionDay = new Date(sessionDate.getFullYear(), sessionDate.getMonth(), sessionDate.getDate());
        if (sessionDay.getTime() >= today.getTime()) {
          // 今天
          groups[0].sessions.push(session);
        } else if (sessionDay.getTime() >= yesterday.getTime()) {
          // 昨天
          groups[1].sessions.push(session);
        } else if (sessionDay.getTime() >= weekAgo.getTime()) {
          // 一周内
          groups[2].sessions.push(session);
        } else {
          // 更早时间
          groups[3].sessions.push(session);
        }
      });
      this.groupedSessions = groups;
    },
    openDify(row) {
      // getAppDifyUrl({ appId: row.appId }).then((res) => {
      //更新应用会话ID
      let conversationIdInfo =
        JSON.parse(localStorage.getItem("conversationIdInfo")) || {};
      conversationIdInfo[row.appId] = {
        ...conversationIdInfo[row.appId],
        [row.userId]: row.conversationId,
      };
      localStorage.setItem(
        "conversationIdInfo",
        JSON.stringify(conversationIdInfo),
      );
      this.$router.push({
        name: "difyPage",
        query: {
          // url: res.data,
          conversationId: row.conversationId,
          appId: row.appId,
        },
      });
      // });
    },
    deleteSession(row) {
      this.confirmMessage(this.$t("session.delTip")).then(() => {
        deleteSession({
          conversationId: row.conversationId,
          appId: row.appId,
        }).then((res) => {
          this.successMsg(this.$t("msg.delete"));
          this.getSessionList();
          if (row.conversationId == this.conversationId) {
            this.$router.push({
              name: "newDifyPage",
            });
          }
        });
      });
    },
    editName(row) {
      this.promptMessage(
        this.$t("session.pleaseEnterSession"),
        this.$t("common.tips"),
        {
          inputValue: row.conversationName,
          inputValidator: (val) => {
            if (!val) {
              return this.$t("placeholder.place");
            }
            if (val.length > 32) {
              return this.$t("tips.checkMaxLen", { max: 32 });
            }
          },
        },
        true,
        false,
      ).then(({ value }) => {
        editSession({
          appId: row.appId,
          conversationId: row.conversationId,
          newName: value,
        }).then((res) => {
          this.successMsg(this.$t("msg.save"));
          this.getSessionList();
        });
      });
    },
  },
};
</script>
<style lang="scss" scoped>
// 隐藏滚动条
::v-deep .scrollbar-wrapper {
  overflow-x: hidden !important;
}

::v-deep .el-scrollbar__bar {
  display: none !important;
}

::v-deep .el-scrollbar__wrap {
  overflow-x: hidden !important;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */

  &::-webkit-scrollbar {
    display: none; /* Chrome, Safari and Opera */
  }
}

// 折叠状态下隐藏元素
.collapsed-hidden {
  display: none !important;
}

.chat-list {
  padding-right: 12px;
  .title {
    font-size: 12px;
    font-weight: bold;
    padding-left: 20px;
    color: #666666;
    height: 40px;
    line-height: 40px;
  }
  .group-header {
    font-size: 12px;
    font-weight: bold;
    padding-left: 20px;
    color: #666666;
    height: 24px;
    line-height: 24px;
  }
  span {
    padding-left: 0px;
  }
  .chat-item {
    font-size: 14px;
    padding-left: 20px;
    padding-right: 12px;
    height: 50px;
    line-height: 50px;
    color: var(--custom-menu-color);
    cursor: pointer;
    display: flex;
    align-items: center;
    margin: 4px 8px;
    border-radius: 8px;
    transition: all 0.3s ease;

    // 折叠状态下的样式
    &.collapsed {
      padding-left: 12px;
      padding-right: 12px;
      margin: 4px auto;
      width: 40px;
      height: 40px;
      justify-content: center;

      .app-icon {
        margin-right: 0;
      }
    }
    &:hover {
      background-color: var(--custom-sub-menu-hover);
      color: var(--custom-menu-color);
      transform: translateX(2px);
      // 鼠标悬停时显示操作按钮
      ::v-deep .operate-btns {
        opacity: 1;
      }

      // 折叠状态下的悬停效果
      &.collapsed {
        transform: scale(1.1);
      }
    }
    &.is-active {
      background-color: var(--custom-sub-menu-hover);
      color: var(--custom-menu-color-active);
      font-weight: 600;
      // 激活状态时也显示操作按钮
      ::v-deep .operate-btns {
        opacity: 1;
        .el-button {
          color: var(--custom-menu-color-active) !important;
        }
        .el-dropdown {
          .el-icon-more {
            color: var(--custom-menu-color-active) !important;
          }
        }
      }

      // 折叠状态下的激活效果
      &.collapsed {
        background-color: var(--custom-sub-menu-hover);

        .app-icon {
          .emoji-icon {
            color: #ffffff;
          }
          img {
            filter: brightness(1.2);
          }
        }
      }
    }
    .app-icon {
      flex: 0 0 30px;
      width: 30px;
      height: 30px;
      margin-right: 10px;
      display: flex;
      align-items: center;
      justify-content: center;
      .emoji-icon {
        display: inline-block;
        width: 100%;
        height: 30px;
        border-radius: 4px;
        line-height: 30px;
        text-align: center;
        font-size: 20px;
      }
      img {
        width: 100%;
        height: 100%;
        border-radius: 4px;
        object-fit: cover;
      }
    }
    .chat-item-name {
      flex: 1;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      align-items: center;
      margin-right: 4px;
      min-width: 0;
    }
    .chait-item-remove {
      padding: 0px 5px;
    }
    // 操作按钮样式优化
    ::v-deep .operate-btns {
      opacity: 0;
      transition: opacity 0.2s ease;
      .el-button {
        color: #999 !important;
        &:hover {
          color: #666 !important;
        }
      }
      .el-dropdown {
        .el-icon-more {
          color: #999 !important;
        }
        &:hover .el-icon-more {
          color: #666 !important;
        }
      }
    }
  }
}

/* 抽屉切换按钮样式 */
.drawer-toggle-btn {
  position: absolute;
  bottom: 16px;
  right: 16px;
  width: 32px;
  height: 32px;
  background-color: var(--custom-menu-color-active, #1C6CDD);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  z-index: 1000;

  &:hover {
    background-color: var(--custom-menu-color-active, #1C6CDD);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    transform: scale(1.05);
  }

  .drawer-icon {
    width: 16px;
    height: 16px;
    fill: #ffffff;
    transition: transform 0.3s ease;

    &.is-collapsed {
      transform: rotate(180deg);
    }
  }
}

/* AI 侧边栏容器样式 */
.ai-sidebar-container {
  position: relative;
  height: 100%;
}
</style>

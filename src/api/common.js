import request from "@/utils/request";

/**
 * 获取认证模式
 */
export function getAuthModeData() {
  return new Promise((resolve, reject) => {
    resolve({
      code: "0",
      status: "0",
      data: {
        authModeId: "1",
        authModeList: [
          {
            id: "1",
            authModeCnName: "用户名/口令",
            authModeEnName: "PASSWORD",
            initStatus: 0,
            routePath: "loginUser",
          },
          {
            id: "3",
            authModeCnName: "公司AD域登录",
            authModeEnName: "AD Login",
            initStatus: 1,
            routePath: "loginLdap",
          },
        ],
      },
    });
  });
  return request({
    url: "/auth/mode/list",
    method: "get",
  });
}
/**
 * 获取系统信息
 */
export function getSysConfig() {
  return request({
    url: "/login/sysConfig",
    method: "get",
  });
}

// 获取系统字典
export function getSysDict(typeCode) {
  return request({
    url: "/secDict/query",
    method: "get",
    params: {
      typeCode,
    },
  });
}
// 获取多个系统字典
export function getSysDicts(data) {
  return request({
    url: "/secDict/queryByTypeCode",
    method: "post",
    headers: {
      repeatSubmit: false,
    },
    data,
  });
}

// 登录页获取产品信息
export function getProductInfo(params) {
  return request({
    url: "/login/getVersion",
    method: "get",
    headers: {
      repeatSubmit: false,
    },
    params,
  });
}
